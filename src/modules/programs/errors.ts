const HUMAN_ERROR_MESSAGES = {
  notUniqueCode: 'Code isn’t unique',
  notUniqueStudent: 'Student isn’t unique',
  hasChildrenError:
    'You cannot set #{status} item status because it has children',
  draftStatusError: 'You can’t come back to draft status',
  onlyOneError: 'Only one staff in first group allowed',
  subjectCapacity:
    '#{activityName} capacity is set to #{capacity}, #{name} capacity cannot be higher',
  programCapacityWithSubject:
    '#{program} capacity is set to #{capacity}, #{subject} capacity cannot be higher',
  programCapacity:
    '#{programName} capacity is set to #{capacity}, #{activityName} capacity cannot be higher',
  studentGroupCapacity:
    '#{program} capacity is set to #{capacity}, #{name} capacity cannot be higher',
  onlyDraft: 'Only in Draft status you can update intake',
  intakeNotFound: 'Intake is not found',
  intakeIsCompleted: 'Could not update, Intake is completed',
  intakeIsLinkedToTt: 'Intake already linked to the timetable',
  intakeIsDeleted: 'Could not update, Intake is deleted',
  intakeIsDeletedOrLinkedToTt:
    'Could not update, Intake is deleted and linked to Timetable',
  assessmentNotAvailable: 'For #{name} assessment is not available ',
  studentSubmissionError: "Submission for this task doesn't available",
  taskAttachmentsRange:
    'Attachments To should be greater then (or equal) Attachments From',
  taskAssessmentChanged: 'Task Assessment Type changed, reload the page',
  multipleTimetable: 'Only one timetable update in one time',
  intakeLinkedToTimetable: 'Intake already linked to timetable',
  intakeLinkedToLearningSpace: 'Intake already linked to learning space',
  classLinkedToTimetable: 'Class already linked to timetable',
  classLinkedToLearningSpace: 'Class already linked to learning space',
  unsupportedTimetableType: 'Timetable type is not supported',
  blockTimetablePeriodIsNotPassed:
    'Block Timetable start and end duration values are not passed',
  nonBlockTimetableCyclesIsNotPassed:
    'Non-Block timetable cycles are not passed',
  enrolmentIsNotFound: 'Enrolment is not found',
  studentIsNotAssignedOnParentSubject:
    '#{student} does not have active enrolment in #{subject}',
  studentIsNotAssignedOnParentProgram:
    '#{student} does not have active enrolment in #{program}',
  studentGroupEnrolmentIsNotAvailable:
    'Student Group Enrolment is not available',
  notSupportedIntakeStudentType:
    'Provided intake student type is not supported by the system',
  programGroupTypeCouldNotBeResolved:
    'Program Group Type could not be resolved with provided parameters',
  staffIsNotAssignedOnProgram: 'Staff is not assigned on parent Program',
  staffIsNotAssignedOnSubject: 'Staff is not assigned on parent Subject',
  notSupportedArguments: 'Not Supported Arguments',
  timetableTemplateDaysAreNotProvided:
    'Timetable template days are not provided',
  timetableCyclesAreNotProvided: 'Timetable cycles are not provided',
  noMatchingOrgStructuresInTtAndIntake:
    'No matching organisation structure levels in intake and timetable settings',
  learningPlanInUse:
    'This learning plan includes tasks from the Learning Space and cannot be moved.',
};

export { HUMAN_ERROR_MESSAGES };
