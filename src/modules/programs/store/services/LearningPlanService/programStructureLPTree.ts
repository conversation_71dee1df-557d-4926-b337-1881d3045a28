import { IConnection } from 'edana-microservice';
import { map, includes, uniqBy } from 'lodash';

import LearningPlan from '../../entities/LearningPlan';
import LearningPlanCategory from '../../entities/LearningPlanCategory';
import LearningPlanTask from '../../entities/LearningPlanTask';
import Program from '../../entities/Program';
import ProgramStructureSubject from '../../entities/ProgramStructureSubject';
import { uniqMap } from '../../../../..//modules/core/helpers';
import {
  SUBJECT,
  LEARNING_PLAN,
  LP_CATEGORY,
  LP_MODULE,
  CONTENT,
} from '../../../model/CurriculumStructureViewTypes';
import { ACTIVE } from '../../../../../model/Status';
import { PUBLISHED } from '../../../../../modules/programs/model/LearningPlanStatus';

export default async function programStructureLPTree(
  {
    connection,
  }: {
    connection: IConnection;
  },
  {
    programGroupId,
    view,
    tenantId,
  }: {
    programGroupId: number;
    view: string;
    tenantId: number;
  },
) {
  const programs = includes(
    [
      SUBJECT.value,
      LEARNING_PLAN.value,
      LP_CATEGORY.value,
      LP_MODULE.value,
      CONTENT.value,
    ],
    view,
  )
    ? await Program.loadBy(
        connection,
        {
          programGroupId,
          tenantId,
          status: ACTIVE,
        },
        { order: 'SEQUENCE ASC' },
      )
    : [];

  const programStructureSubjects = includes(
    [LEARNING_PLAN.value, LP_CATEGORY.value, LP_MODULE.value, CONTENT.value],
    view,
  )
    ? await ProgramStructureSubject.loadBy(
        connection,
        {
          programGroupId,
          tenantId,
          status: ACTIVE,
        },
        { order: 'SEQUENCE ASC' },
      )
    : [];

  const learningPlans = includes(
    [LP_CATEGORY.value, LP_MODULE.value, CONTENT.value],
    view,
  )
    ? await LearningPlan.loadBy(
        connection,
        {
          programGroupId,
          tenantId,
          status: PUBLISHED,
        },
        { order: 'NAME ASC' },
      )
    : [];

  let categories = includes([LP_MODULE.value, CONTENT.value], view)
    ? await LearningPlanCategory.loadBy(
        connection,
        {
          learningPlanId: uniqMap(learningPlans),
          tenantId,
          status: PUBLISHED,
        },
        { order: 'SEQUENCE ASC' },
      )
    : [];

  const categoriesChildren =
    (await LearningPlanCategory.loadTreeChildren(
      connection,
      categories,
      undefined,
    )) || [];

  categories = uniqBy([...categories, ...categoriesChildren], 'id');

  const tasks = includes([CONTENT.value], view)
    ? await LearningPlanTask.loadBy(
        connection,
        {
          learningPlanCategoryId: uniqMap(categories),
          tenantId,
          status: PUBLISHED,
        },
        { order: 'SEQUENCE ASC' },
      )
    : [];

  return [
    ...map(programs, s => ({ ...s, __type: 'Program' })),
    ...map(programStructureSubjects, s => ({
      ...s,
      __type: 'ProgramStructureSubject',
    })),
    ...map(learningPlans, s => ({ ...s, __type: 'LearningPlan' })),
    ...map(categories, s => ({ ...s, __type: 'LearningPlanCategory' })),
    ...map(tasks, s => ({ ...s, __type: 'LearningPlanTask' })),
  ];
}
