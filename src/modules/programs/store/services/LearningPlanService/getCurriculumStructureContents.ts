import { IConnection } from 'edana-microservice';
import { map } from 'lodash';
import deserializeDbObject from '../../../../../utils/deserializeDbObject';
import { ACTIVE_ID } from '../../../../../modules/core/model/StatusWithDraft';

export default async function getCurriculumStructureContents(
  {
    connection,
  }: {
    connection: IConnection;
  },
  {
    ids,
    tenantId,
    first,
    count,
    searchQuery,
  }: {
    ids: number[];
    tenantId: number;
    first: number;
    count: number;
    searchQuery: string;
  },
) {
  const query = `
  SELECT  
    ei.ID AS LESSON_GROUP_ID, 
    ei.NAME AS LESSON_GROUP_NAME, 
    ec.ID AS LESSON_ID, 
    ecc.CONTENT AS LESSON_NAME,
    ec.STATUS AS LESSON_STATUS,
    pp.ID AS PROGRAM_ID, 
    pp.NAME AS PROGRAM_NAME, 
    ppss.ID AS SUBJECT_ID, 
    ppss.NAME AS SUBJECT_NAME, 
    plp.ID AS LEARNING_PLAN_ID, 
    plp.NAME AS LEARNING_PLAN_NAME,   
    er.LIBRARY_ID AS LIBRARY_ID,   
    (
        SELECT LTRIM(SYS_CONNECT_BY_PATH(ID, ' > '), ' > ') 
        FROM PG_LEARNING_PLAN_CATEGORY plpc1 
        WHERE CONNECT_BY_ISLEAF = 1 AND plpc1.ID = plpc.ID
        START WITH PARENT_ID IS NULL 
        CONNECT BY PRIOR ID = PARENT_ID
    ) AS LEARNING_PLAN_CATEGORY_ID, 
    (
        SELECT LTRIM(SYS_CONNECT_BY_PATH(NAME, ' > '), ' > ') 
        FROM PG_LEARNING_PLAN_CATEGORY plpc1 
        WHERE CONNECT_BY_ISLEAF = 1 AND plpc1.ID = plpc.ID
        START WITH PARENT_ID IS NULL 
        CONNECT BY PRIOR ID = PARENT_ID
    ) AS LEARNING_PLAN_CATEGORY_NAME, 
    plpt.ID AS MODULE_ID, 
    plpt.NAME AS MODULE_NAME,
    pltxec.SEQUENCE AS ORDER_SEQ
FROM PG_PROGRAM pp  
JOIN PG_PROGRAM_STRUCT_SUBJECT ppss ON ppss.PROGRAM_ID = pp.ID  
JOIN PG_LEARNING_PLAN plp ON plp.PROG_STRUCT_SUBJECT_ID = ppss.ID 
JOIN PG_LEARNING_PLAN_CATEGORY plpc ON plpc.LEARNING_PLAN_ID = plp.ID 
JOIN PG_LEARNING_PLAN_TASK plpt ON plpt.LEARNING_PLAN_CATEGORY_ID = plpc.ID  
LEFT JOIN PG_LP_TASK_X_ECN_CONTENT pltxec
    ON pltxec.LP_TASK_ID = plpt.ID 
    AND pltxec.TENANT_ID = :tenantId
LEFT JOIN ECN_CONTENT ec ON pltxec.CONTENT_ID = ec.ID AND ec.STATUS = :activeId
LEFT JOIN ECN_CONTENT_CLOB ecc ON ecc.CONTENT_ID = ec.ID AND ecc.ATTRIBUTE_ID = 5
LEFT JOIN ECN_ITEM ei ON ecc.ITEM_ID = ei.ID
LEFT JOIN ECN_RESOURCE er ON ei.RESOURCE_ID = er.ID
WHERE plpt.ID IN (${ids.join(',')})
  AND plpt.ECN_INTEGRATION = 1
  AND NOT EXISTS (SELECT 1 FROM PG_LEARNING_PLAN_CATEGORY plpc2 WHERE plpc2.PARENT_ID = plpc.ID)
  AND pltxec.ID IS NOT NULL
  AND (
    LOWER(ei.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
    LOWER(ecc.CONTENT) LIKE LOWER('%' || '${searchQuery}' || '%')
  )
UNION ALL
SELECT  
    ei.ID AS LESSON_GROUP_ID, 
    ei.NAME AS LESSON_GROUP_NAME, 
    ec2.ID AS LESSON_ID, 
    ecc2.CONTENT AS LESSON_NAME,
    ec2.STATUS AS LESSON_STATUS,
    pp.ID AS PROGRAM_ID, 
    pp.NAME AS PROGRAM_NAME, 
    ppss.ID AS SUBJECT_ID, 
    ppss.NAME AS SUBJECT_NAME, 
    plp.ID AS LEARNING_PLAN_ID, 
    plp.NAME AS LEARNING_PLAN_NAME,   
    er.LIBRARY_ID AS LIBRARY_ID,   
    (
        SELECT LTRIM(SYS_CONNECT_BY_PATH(ID, ' > '), ' > ') 
        FROM PG_LEARNING_PLAN_CATEGORY plpc1 
        WHERE CONNECT_BY_ISLEAF = 1 AND plpc1.ID = plpc.ID
        START WITH PARENT_ID IS NULL 
        CONNECT BY PRIOR ID = PARENT_ID
    ) AS LEARNING_PLAN_CATEGORY_ID, 
    (
        SELECT LTRIM(SYS_CONNECT_BY_PATH(NAME, ' > '), ' > ') 
        FROM PG_LEARNING_PLAN_CATEGORY plpc1 
        WHERE CONNECT_BY_ISLEAF = 1 AND plpc1.ID = plpc.ID
        START WITH PARENT_ID IS NULL 
        CONNECT BY PRIOR ID = PARENT_ID
    ) AS LEARNING_PLAN_CATEGORY_NAME, 
    plpt.ID AS MODULE_ID, 
    plpt.NAME AS MODULE_NAME,
    (pltxei.SEQUENCE * 1000 + ec2.SEQUENCE) AS ORDER_SEQ
FROM PG_PROGRAM pp  
JOIN PG_PROGRAM_STRUCT_SUBJECT ppss ON ppss.PROGRAM_ID = pp.ID  
JOIN PG_LEARNING_PLAN plp ON plp.PROG_STRUCT_SUBJECT_ID = ppss.ID 
JOIN PG_LEARNING_PLAN_CATEGORY plpc ON plpc.LEARNING_PLAN_ID = plp.ID 
JOIN PG_LEARNING_PLAN_TASK plpt ON plpt.LEARNING_PLAN_CATEGORY_ID = plpc.ID  
LEFT JOIN PG_LP_TASK_X_ECN_ITEM pltxei
    ON pltxei.LP_TASK_ID = plpt.ID 
    AND pltxei.TENANT_ID = :tenantId
LEFT JOIN ECN_ITEM ei ON pltxei.ITEM_ID = ei.ID
LEFT JOIN ECN_CONTENT ec2 ON ec2.ITEM_ID = ei.ID
LEFT JOIN ECN_CONTENT_CLOB ecc2 ON ecc2.CONTENT_ID = ec2.ID AND ecc2.ATTRIBUTE_ID = 5 
LEFT JOIN ECN_RESOURCE er ON ei.RESOURCE_ID = er.ID
WHERE plpt.ID IN (${ids.join(',')})
  AND plpt.ECN_INTEGRATION = 2
  AND NOT EXISTS (
    SELECT 1 FROM PG_LEARNING_PLAN_CATEGORY plpc2 
    WHERE plpc2.PARENT_ID = plpc.ID
  )
  AND pltxei.ID IS NOT NULL
  AND (
    LOWER(ei.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
    LOWER(ecc2.CONTENT) LIKE LOWER('%' || '${searchQuery}' || '%')
  )
ORDER BY ORDER_SEQ
OFFSET :first ROWS FETCH NEXT :count ROWS ONLY
`;

  const { rows = [] } = await connection.execute(query, {
    tenantId,
    first,
    count,
    activeId: ACTIVE_ID,
  });
  return map(rows, x =>
    deserializeDbObject<ICurriculumStructureContentsTable>(x),
  );
}

export interface ICurriculumStructureContentsTable {
  lessonGroupId: number;
  lessonGroupName: string;
  lessonId: number;
  lessonName: string;
  lessonStatus: number;
  programId: number;
  programName: string;
  subjectId: number;
  subjectName: string;
  learningPlanId: number;
  learningPlanName: string;
  learningPlanCategoryId: string;
  learningPlanCategoryName: string;
  moduleId: number;
  moduleName: string;
  libraryId: number;
}
