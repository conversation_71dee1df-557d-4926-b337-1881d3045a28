const {
  errors: { HumanError },
} = require('edana-microservice');
const { isEmpty } = require('lodash');
const LearningPlan = require('../../entities/LearningPlan').default;
const ProgramStructureSubject = require('../../entities/ProgramStructureSubject')
  .default;
const LPXClass = require('../../entities/LPXClass').default;

module.exports = async (
  { connection },
  { id, params: { learningStatus, subjectId, ...params }, tenantId },
) => {
  const subject = await ProgramStructureSubject.findBy(connection, {
    id: subjectId,
    tenantId,
  });

  const currentLP = await LearningPlan.findBy(connection, {
    id,
    tenantId,
  });

  if (currentLP.subjectId !== subject.id) {
    const exists = await LPXClass.loadBy(connection, {
      learningPlanId: id,
      tenantId,
    });
    if (!isEmpty(exists)) {
      throw new HumanError('learningPlanInUse');
    }
  }
  const learningPlan = await LearningPlan.update(connection, {
    ...params,
    status: learningStatus,
    subjectId: subject.id,
    programGroupId: subject.programGroupId,
    tenantId,
    id,
  });

  await connection.commit();

  return learningPlan;
};
