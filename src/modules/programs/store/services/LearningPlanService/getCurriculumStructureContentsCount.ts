import { IConnection } from 'edana-microservice';
import { ACTIVE_ID } from '../../../../../modules/core/model/StatusWithDraft';

export default async function getCurriculumStructureContentsCount(
  {
    connection,
  }: {
    connection: IConnection;
  },
  {
    ids,
    tenantId,
    searchQuery,
  }: {
    ids: number[];
    tenantId: number;
    searchQuery: string;
  },
) {
  const countQuery = `
  SELECT COUNT(*) AS total
  FROM (
    SELECT 1
    FROM PG_PROGRAM pp  
    JOIN PG_PROGRAM_STRUCT_SUBJECT ppss ON ppss.PROGRAM_ID = pp.ID  
    JOIN PG_LEARNING_PLAN plp ON plp.PROG_STRUCT_SUBJECT_ID = ppss.ID 
    JOIN PG_LEARNING_PLAN_CATEGORY plpc	ON plpc.LEARNING_PLAN_ID = plp.ID 
    JOIN PG_LEARNING_PLAN_TASK plpt ON plpt.LEARNING_PLAN_CATEGORY_ID = plpc.ID  
    LEFT JOIN PG_LP_TASK_X_ECN_CONTENT pltxec
      ON pltxec.LP_TASK_ID = plpt.ID 
      AND plpt.ID IN (${ids.join(',')}) 
      AND plpt.ECN_INTEGRATION = 1 AND pltxec.TENANT_ID = :tenantId 
    LEFT JOIN ECN_CONTENT ec ON pltxec.CONTENT_ID = ec.ID AND ec.STATUS = :activeId
    LEFT JOIN ECN_CONTENT_CLOB ecc ON ecc.CONTENT_ID = ec.ID AND ecc.ATTRIBUTE_ID = 5 
    LEFT JOIN ECN_ITEM ei1 ON ecc.ITEM_ID = ei1.ID
    LEFT JOIN PG_LP_TASK_X_ECN_ITEM pltxei 
      ON pltxei.LP_TASK_ID = plpt.ID 
      AND plpt.ID IN (${ids.join(',')}) 
      AND plpt.ECN_INTEGRATION = 2 AND pltxei.TENANT_ID = :tenantId 
    LEFT JOIN ECN_ITEM ei ON pltxei.ITEM_ID = ei.ID 
    LEFT JOIN ECN_CONTENT ec2 ON ec2.ITEM_ID = ei.ID 
    LEFT JOIN ECN_CONTENT_CLOB ecc2 ON ecc2.CONTENT_ID = ec2.ID AND ecc2.ATTRIBUTE_ID = 5 
    WHERE plpt.ID IN (${ids.join(',')})
      AND (
        LOWER(ei1.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
        LOWER(ei.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
        LOWER(ecc.CONTENT) LIKE LOWER('%' || '${searchQuery}' || '%') OR
        LOWER(ecc2.CONTENT) LIKE LOWER('%' || '${searchQuery}' || '%')
      )
      AND NOT EXISTS (
        SELECT 1 
        FROM PG_LEARNING_PLAN_CATEGORY plpc2 
        WHERE plpc2.PARENT_ID = plpc.ID
      )
      AND (pltxec.ID IS NOT NULL OR pltxei.ID IS NOT NULL)
  ) sub
`;

  const { rows = [] } = await connection.execute(countQuery, {
    tenantId,
    activeId: ACTIVE_ID,
  });

  return rows?.[0]?.TOTAL ?? 0;
}
