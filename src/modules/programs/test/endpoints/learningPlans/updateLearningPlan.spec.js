const { describe, it, afterEach, beforeEach } = require('mocha');
require('chai').should();
const sinon = require('sinon');

const LearningPlan = require('../../../store/entities/LearningPlan').default;
const ProgramStructureSubject = require('../../../store/entities/ProgramStructureSubject')
  .default;

describe(`update learning plan`, () => {
  let sandBox;
  let context;
  let updateLearningPlan;

  beforeEach(() => {
    sandBox = sinon.createSandbox();

    context = {
      connection: {
        commit: sandBox.stub(),
      },
      tenantId: 4,
    };

    sandBox.stub(LearningPlan, 'update');
    sandBox.stub(ProgramStructureSubject, 'findBy');

    ProgramStructureSubject.findBy
      .withArgs(context.connection, { id: 40, tenantId: 4 })
      .returns({ id: 40, tenantId: 4, programGroupId: 69 });

    LearningPlan.update
      .withArgs(context.connection, {
        id: 123,
        name: 'string name',
        tenantId: 4,
        programGroupId: 69,
        subjectId: 40,
        status: 'DRAFT',
      })
      .returns({
        id: 123,
        name: 'string name',
        tenantId: 4,
        programGroupId: 69,
        subjectId: 40,
        status: 'DRAFT',
      });

    updateLearningPlan = require('../../../endpoints/learningPlans/updateLearningPlan');
  });

  afterEach(() => {
    sandBox.reset();
    sandBox.restore();
  });

  it.skip(`should call updating of learning plan`, async () => {
    await updateLearningPlan(context, {
      id: 123,
      params: {
        learningStatus: 'DRAFT',
        subjectId: 40,
        name: 'string name',
      },
      tenantId: 4,
    });

    sinon.assert.calledOnce(ProgramStructureSubject.findBy);
    sinon.assert.calledOnce(LearningPlan.update);
    sinon.assert.calledOnce(context.connection.commit);

    sinon.assert.callOrder(
      ProgramStructureSubject.findBy,
      LearningPlan.update,
      context.connection.commit,
    );
  });

  it.skip(`should return updated learning plan`, async () => {
    const plan = await updateLearningPlan(context, {
      id: 123,
      params: {
        learningStatus: 'DRAFT',
        subjectId: 40,
        name: 'string name',
      },
      tenantId: 4,
    });

    plan.should.be.deep.equal({
      id: 123,
      name: 'string name',
      tenantId: 4,
      programGroupId: 69,
      subjectId: 40,
      status: 'DRAFT',
    });
  });
});
